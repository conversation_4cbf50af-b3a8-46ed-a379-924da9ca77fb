## Reorganization and Tidying Plan

### Goal
Reorganize the codebase to improve clarity and maintainability while preserving all current functionality. No functional changes are intended—only file moves, import path updates, and optional compatibility shims.

---

## 1) Current Structure (high-level)
This reflects the repository at the time of planning (top-level and selected subfolders):

- Root
  - main.py
  - ml_core.py
  - enhanced_preprocessing.py
  - data_handler.py
  - data_leakage_detector.py
  - optimized_base_model_fix.py
  - reporting.py
  - mlr_utils.py
  - config_handler.py
  - README.md, requirements.txt, settings.json
  - debug_slow_training.py, gradient_diagnostics.py, quick_training_speedup_guide.py
  - config/
  - core_code/ (empty)
  - docs/
  - example/
  - models/
    - neuralnet.py
    - advanced_models/
  - utils/
    - display_utils.py, gpu_acceleration.py, gpu_fallback.py, hyperparameter_tuning.py,
      memory_optimization.py, metrics.py, mixed_precision_utils.py, optimization.py,
      output_formatter.py, performance_monitor.py, prediction_data_processor.py,
      stability_core.py, training_optimization.py, visualization_advanced.py,
      well_progress.py, xgboost_gpu_utils.py
  - Las/, archives/, catboost_info/

Notes:
- core_code/ already exists but is empty.
- utils/ is already populated with many helper/tooling modules.
- models/ holds model definitions; these are typically kept as their own package.

---

## 2) Target Structure
We will group core ML pipeline and preprocessing under core_code/, and move all helper/utility modules to utils/.

- core_code/
  - ml_core.py  (from root)
  - optimized_base_model_fix.py (from root)
  - preprocessing/
    - enhanced_preprocessing.py (from root)
    - data_handler.py (from root)
    - data_leakage_detector.py (from root)
- utils/
  - (already present files remain)
  - mlr_utils.py (from root)
  - reporting.py (from root)
  - config_handler.py (from root)
  - diagnostics/
    - gradient_diagnostics.py (from root)
    - debug_slow_training.py (from root)
  - guides/
    - quick_training_speedup_guide.py (from root)
- models/ (unchanged)
- example/, docs/, config/, Las/, archives/, catboost_info/ (unchanged)
- Entry points (e.g., main.py) remain at root

Backward-compatibility (optional but recommended):
- Keep thin shim modules at the old locations that import and re-export from their new locations with a DeprecationWarning. This allows existing external scripts to continue working temporarily.

---

## 3) What Counts as a Utility File?
Utility files are modules that do not define domain-layer business logic or pipeline orchestration, but rather offer reusable helpers:
- Cross-cutting helpers (formatting, display, logging, reporting wrappers)
- Performance/optimization utilities (GPU acceleration helpers, mixed precision, monitoring)
- Small, general-purpose functions (metrics, hyperparameter helpers)
- Developer-facing diagnostics or guides (debug scripts, diagnostic tools)

Based on names and current layout:
- Utilities: mlr_utils.py, reporting.py, config_handler.py, gradient_diagnostics.py, debug_slow_training.py, quick_training_speedup_guide.py (plus the existing utils/* files)
- Core ML: ml_core.py, optimized_base_model_fix.py
- Preprocessing: enhanced_preprocessing.py, data_handler.py, data_leakage_detector.py

---

## 4) Step-by-step Implementation Instructions

Important: Use git mv to preserve history. Perform changes on a feature branch and run tests after each move.

1) Create a branch
- git checkout -b chore/reorg-core-utils-structure

2) Create target folders
- Create directories if missing:
  - core_code/preprocessing/
  - utils/diagnostics/
  - utils/guides/

3) Move core and preprocessing modules
- git mv ml_core.py core_code/ml_core.py
- git mv optimized_base_model_fix.py core_code/optimized_base_model_fix.py
- git mv enhanced_preprocessing.py core_code/preprocessing/enhanced_preprocessing.py
- git mv data_handler.py core_code/preprocessing/data_handler.py
- git mv data_leakage_detector.py core_code/preprocessing/data_leakage_detector.py

4) Move utilities
- git mv mlr_utils.py utils/mlr_utils.py
- git mv reporting.py utils/reporting.py
- git mv config_handler.py utils/config_handler.py
- mkdir -p utils/diagnostics && git mv gradient_diagnostics.py utils/diagnostics/gradient_diagnostics.py
- mkdir -p utils/guides && git mv debug_slow_training.py utils/diagnostics/debug_slow_training.py
- git mv quick_training_speedup_guide.py utils/guides/quick_training_speedup_guide.py

5) Update imports (see mapping in Section 5)
- Update all internal imports using the mapping below.
- Prefer absolute imports from the repository root, e.g., from core_code.preprocessing.data_handler import ...

6) Optional: Add backward-compatibility shims (temporary)
- Create tiny files at the old paths re-exporting the new modules, e.g., for ml_core.py at root:
  - import warnings; warnings.warn("ml_core moved to core_code.ml_core", DeprecationWarning)
  - from core_code.ml_core import *
- Plan to remove these after consumers have migrated (set a deadline in docs/CHANGELOG).

7) Run validation steps (Section 6)

8) Open PR, get reviews, and merge.

---

## 5) Import Update Mapping
Search-and-replace patterns to apply across the codebase (including notebooks and scripts). Use smart, reviewed edits—avoid blind replacements.

Core and preprocessing moves:
- from ml_core import X  -> from core_code.ml_core import X
- import ml_core         -> import core_code.ml_core as ml_core
- from enhanced_preprocessing import Y -> from core_code.preprocessing.enhanced_preprocessing import Y
- import enhanced_preprocessing -> import core_code.preprocessing.enhanced_preprocessing as enhanced_preprocessing
- from data_handler import Z -> from core_code.preprocessing.data_handler import Z
- import data_handler -> import core_code.preprocessing.data_handler as data_handler
- from data_leakage_detector import A -> from core_code.preprocessing.data_leakage_detector import A
- import data_leakage_detector -> import core_code.preprocessing.data_leakage_detector as data_leakage_detector
- from optimized_base_model_fix import B -> from core_code.optimized_base_model_fix import B

Utilities:
- from mlr_utils import U -> from utils.mlr_utils import U
- import mlr_utils -> import utils.mlr_utils as mlr_utils
- from reporting import R -> from utils.reporting import R
- import reporting -> import utils.reporting as reporting
- from config_handler import C -> from utils.config_handler import C
- import config_handler -> import utils.config_handler as config_handler

Also check and update any relative imports inside the moved files if they exist.

Suggested commands to find references:
- git grep -nE "(^|\W)(ml_core|enhanced_preprocessing|data_handler|data_leakage_detector|optimized_base_model_fix|mlr_utils|reporting|config_handler)(\W|$)"

---

## 6) Validation and Testing Steps

After moving files and updating imports, validate that functionality is preserved.

1) Static checks
- Run a fast import check:
  - python - << 'PY'\nimport importlib, sys
modules = [
  'core_code.ml_core',
  'core_code.optimized_base_model_fix',
  'core_code.preprocessing.enhanced_preprocessing',
  'core_code.preprocessing.data_handler',
  'core_code.preprocessing.data_leakage_detector',
  'utils.mlr_utils', 'utils.reporting', 'utils.config_handler',
]
failed = []
for m in modules:
    try:
        importlib.import_module(m)
        print('OK', m)
    except Exception as e:
        failed.append((m, e))
        print('FAIL', m, e)
print('\nFAILURES:' if failed else '\nAll imports OK')
if failed:
    sys.exit(1)
PY

- Optional: run ruff/flake8/mypy if configured.

2) Smoke tests
- Run main entry point(s):
  - python main.py (with the usual arguments/configs) and verify it completes key steps without import errors.

3) Targeted functional checks (pick representative flows)
- Data loading and preprocessing (calls into core_code.preprocessing.*)
- Model initialization/training (calls into core_code.ml_core and models/*)
- Prediction/metrics/reporting (uses utils/* helpers)

4) Example scripts/notebooks
- Open example/Pypots_quick_start_tutor.py and example notebooks (if any) to update and run import cells if they reference moved modules.

5) Optional: Add a quick pytest sanity test (if test framework exists)
- Verify that at least one end-to-end path still runs under CI or locally.

---

## 7) Backward Compatibility Plan
To reduce disruption for external consumers or notebooks referencing old paths, add shim modules (temporary):
- Old path: ml_core.py (root)
  - re-export from core_code.ml_core and warn
- Old paths: enhanced_preprocessing.py, data_handler.py, data_leakage_detector.py
  - re-export from core_code.preprocessing.* and warn
- Old paths: mlr_utils.py, reporting.py, config_handler.py
  - re-export from utils.* and warn

Set a removal date (e.g., one release after merge). Document in docs/CHANGELOG and docs/summary_improvement.md.

---

## 8) Risks and Mitigations
- Import path drift or circular imports after moves
  - Mitigation: convert to absolute imports; validate with static import check; add shims.
- Hidden references in notebooks or scripts
  - Mitigation: search notebooks; run example scripts; provide deprecation shims.
- Pickled/serialized artifacts referring to old module paths
  - Mitigation: prefer re-generating artifacts; shims help unpickle temporarily; document migration.
- Tools relying on working directory assumptions (e.g., reading config by relative paths)
  - Mitigation: keep main.py at root; verify relative paths; consider using project-root resolution via __file__ or pathlib.Path(__file__).resolve().
- CI/build caches
  - Mitigation: clear caches after merge; ensure requirements unchanged.

---

## 9) Rollout Checklist
- [ ] Branch created
- [ ] Files moved with git mv
- [ ] Imports updated
- [ ] Optional shims added
- [ ] Static import check passes
- [ ] main.py smoke test passes
- [ ] Example script/notebook imports updated
- [ ] PR created, reviewed, and merged
- [ ] Follow-up: remove shims after deprecation window

---

## 10) Summary of Proposed New Structure (after reorg)

- core_code/
  - ml_core.py
  - optimized_base_model_fix.py
  - preprocessing/
    - enhanced_preprocessing.py
    - data_handler.py
    - data_leakage_detector.py
- utils/
  - (existing files)
  - mlr_utils.py
  - reporting.py
  - config_handler.py
  - diagnostics/
    - gradient_diagnostics.py
    - debug_slow_training.py
  - guides/
    - quick_training_speedup_guide.py
- models/ (unchanged)
- example/, docs/, config/, Las/, archives/, catboost_info/ (unchanged)
- main.py at root (unchanged)

This plan keeps models/ and most top-level directories unchanged, centralizes core and preprocessing code in core_code/, and consolidates helpers under utils/—while preserving functionality and providing a smooth migration path.


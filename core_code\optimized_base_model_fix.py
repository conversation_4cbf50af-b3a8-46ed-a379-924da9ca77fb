# COMPREHENSIVE FIX FOR SLOW TRAINING EPOCHS
# Apply these changes to models/advanced_models/base_model.py

# ========================================
# SECTION 1: OPTIMIZED TRAINING DEFAULTS
# ========================================

def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor, 
        epochs: Optional[int] = None, batch_size: Optional[int] = None,
        patience: int = 10, min_delta: float = 1e-4) -> None:
    """
    OPTIMIZED TRAINING with performance fixes for slow epochs.
    
    Major optimizations applied:
    - Mixed precision (AMP) for 1.5-2x speedup
    - Disabled gradient checkpointing by default
    - Larger batch sizes for better GPU utilization
    - Optimized DataLoader with parallel workers
    - Non-blocking GPU transfers
    - TF32 enabled for matrix operations
    """
    import time
    import torch
    import torch.optim.lr_scheduler as lr_scheduler
    from torch.utils.checkpoint import checkpoint
    from utils.memory_optimization import MemoryOptimizer
    from torch.utils.data import DataLoader, TensorDataset
    
    # PERFORMANCE OPTIMIZATION 1: Enable TF32 and cuDNN optimization
    if torch.cuda.is_available():
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.set_float32_matmul_precision("high") 
        torch.backends.cudnn.benchmark = True
        print("✅ GPU optimizations enabled: TF32, cuDNN benchmark")
    
    # Validate input data
    if not self._validate_input_data(train_data) or not self._validate_input_data(truth_data):
        raise ValueError("Invalid input data format")
    
    # Initialize model if not already done
    if self.model is None:
        self._initialize_model()
        
    # PERFORMANCE OPTIMIZATION 2: Increase default batch size for better GPU utilization
    epochs = epochs or self.epochs
    batch_size = batch_size or max(self.batch_size, 128)  # Minimum 128 instead of 32
    
    print(f"🚀 OPTIMIZED Training {self.__class__.__name__} for {epochs} epochs...")
    print(f"   Training data shape: {train_data.shape}")
    print(f"   Batch size: {batch_size} (optimized for GPU)")
    print(f"   Missing values: {torch.isnan(train_data).sum().item()}")
    
    # Record start time
    start_time = time.time()
    
    # PERFORMANCE OPTIMIZATION 3: Optimized DataLoader with parallel workers
    train_set = self._prepare_data(train_data, truth_data)
    train_dataset = TensorDataset(
        torch.from_numpy(train_set['X']).float(), 
        torch.from_numpy(train_set['X_intact']).float()
    )
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        num_workers=4,              # Parallel data loading
        pin_memory=True,            # Faster GPU transfer  
        persistent_workers=True,    # Reduce worker restart overhead
        prefetch_factor=2           # Prefetch batches
    )
    print(f"✅ Optimized DataLoader: {len(train_loader)} batches, 4 workers, pin_memory=True")
    
    # Initialize optimizer and scheduler
    optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
    scheduler = lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5)
    
    # PERFORMANCE OPTIMIZATION 4: Mixed Precision (AMP) setup
    use_amp = self.model_params.get('use_amp', True)  # Enable by default
    scaler = torch.cuda.amp.GradScaler() if use_amp and torch.cuda.is_available() else None
    if use_amp and torch.cuda.is_available():
        print("✅ Mixed precision (AMP) enabled for ~1.5x speedup")
    
    # PERFORMANCE OPTIMIZATION 5: Disable gradient checkpointing by default
    disable_checkpoint = self.model_params.get('disable_checkpoint', True)  # Changed default to True
    if disable_checkpoint:
        print("✅ Gradient checkpointing DISABLED for faster training")
    else:
        print("⚠️ Gradient checkpointing ENABLED (slower but saves memory)")
    
    # Early stopping variables
    best_loss = float('inf')
    epochs_no_improve = 0
    
    # OPTIMIZED TRAINING LOOP
    for epoch in range(epochs):
        self.model.train()
        epoch_loss = 0.0
        epoch_start = time.time()

        for i, (batch_data, batch_truth) in enumerate(train_loader):
            # PERFORMANCE OPTIMIZATION 6: Non-blocking GPU transfers
            if torch.cuda.is_available():
                batch_data = batch_data.cuda(non_blocking=True)
                batch_truth = batch_truth.cuda(non_blocking=True)
            
            optimizer.zero_grad()
            
            # PERFORMANCE OPTIMIZATION 7: Mixed precision forward pass
            if use_amp and scaler is not None:
                with torch.cuda.amp.autocast():
                    if disable_checkpoint:
                        outputs = self.model(batch_data)
                    else:
                        def checkpoint_fn(inputs):
                            return self.model(inputs)
                        outputs = checkpoint(checkpoint_fn, batch_data)
                    loss = torch.nn.functional.mse_loss(outputs, batch_truth)
                
                scaler.scale(loss).backward()
                scaler.step(optimizer)
                scaler.update()
            else:
                # Standard precision training
                if disable_checkpoint:
                    outputs = self.model(batch_data)
                else:
                    def checkpoint_fn(inputs):
                        return self.model(inputs)
                    outputs = checkpoint(checkpoint_fn, batch_data)
                    
                loss = torch.nn.functional.mse_loss(outputs, batch_truth)
                loss.backward()
                optimizer.step()
            
            epoch_loss += loss.item()
        
        epoch_time = time.time() - epoch_start
        avg_loss = epoch_loss / len(train_loader)
        
        # PERFORMANCE OPTIMIZATION 8: Reduced logging frequency for large epoch counts
        if epoch % max(1, epochs // 20) == 0 or epoch == epochs - 1:
            print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.4f}, Time: {epoch_time:.1f}s")
        
        scheduler.step(avg_loss)
        
        # Early stopping
        if avg_loss < best_loss - min_delta:
            best_loss = avg_loss
            epochs_no_improve = 0
        else:
            epochs_no_improve += 1
            if epochs_no_improve >= patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
    
    self.is_fitted = True
    
    # Record training time and performance metrics
    training_time = time.time() - start_time
    self.training_history['total_training_time'] = training_time
    avg_epoch_time = training_time / (epoch + 1)
    
    print(f"✅ OPTIMIZED {self.__class__.__name__} training completed!")
    print(f"   Total time: {training_time:.2f}s")
    print(f"   Avg epoch time: {avg_epoch_time:.2f}s")
    print(f"   Performance optimizations applied: TF32, AMP, optimized DataLoader")
    
    if torch.cuda.is_available():
        peak_memory = torch.cuda.max_memory_allocated()
        print(f"   Peak GPU memory: {peak_memory / 1024**2:.2f} MB")

# ========================================
# SECTION 2: HYPERPARAMETER OVERRIDES  
# ========================================

# Add this to your main.py or wherever you set hparams:
OPTIMIZED_HPARAMS = {
    'batch_size': 128,              # Increased from 32
    'disable_checkpoint': True,     # Disable for speed
    'use_amp': True,               # Enable mixed precision
    'num_workers': 4,              # Parallel data loading
    'gradient_clip_norm': None,    # Disable initially for speed
    'debug_grad': False,           # Disable debugging
    'early_stopping_patience': 15, # More patience for larger batch sizes
}

# ========================================
# SECTION 3: USAGE INSTRUCTIONS
# ========================================

"""
USAGE INSTRUCTIONS:

1. BACKUP your current base_model.py:
   cp models/advanced_models/base_model.py models/advanced_models/base_model.py.backup

2. REPLACE the fit() method in base_model.py with the optimized version above

3. UPDATE your hparams in main.py:
   hparams_selected.update(OPTIMIZED_HPARAMS)

4. RUN your training and measure epoch times

5. EXPECTED RESULTS:
   - 2-4x faster epoch times
   - Same or better model accuracy
   - More efficient GPU utilization

6. IF MEMORY ISSUES occur:
   - Reduce batch_size to 64
   - Set 'disable_checkpoint': False
   - Set 'use_amp': False

7. MONITOR training stability:
   - Check that loss decreases normally
   - Watch for NaN values (indicates numerical instability)
   - Adjust learning rate if needed
"""
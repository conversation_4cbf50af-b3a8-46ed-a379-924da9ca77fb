# Cleanup Plan: Remove Duplicates and Intermediate Files

## Files to Remove

### 1. Duplicate Shim Files (Backward Compatibility)
Since all imports have been updated, these shim files are no longer needed:
- `ml_core.py` (shim)
- `enhanced_preprocessing.py` (shim)
- `data_handler.py` (shim)
- `data_leakage_detector.py` (shim)
- `optimized_base_model_fix.py` (shim)
- `mlr_utils.py` (shim)
- `reporting.py` (shim)
- `config_handler.py` (shim)

### 2. Python Cache Files
- `__pycache__/` directories (all locations)
- `*.pyc` files
- `core_code/__pycache__/`
- `models/__pycache__/`
- `utils/__pycache__/`

### 3. Debugging/Testing Artifacts
- `catboost_info/` (entire directory - training artifacts)
- `plots/` (if contains only test plots)

### 4. Redundant Documentation Files
- `1_Clean_up_autoencoder_unet_plan.md`
- `2_optimize_saits_and_brits.md`
- `3_reorganization_plan.md` (keep REORGANIZATION_COMPLETE.md instead)
- `List_of_cleaned_file.md`
- `docs/fix_init/` (development documentation)
- `docs/fix_md/` (development documentation)
- `docs/improvement/` (development documentation)
- `docs/optimization/` (development documentation)

### 5. Intermediate Development Files
- `archives/ml_core_phase1_integration.py`
- `docs/archive_analysis/` (analysis files)
- `docs/docs.7z` (compressed archive)
- `docs/REMOVE_AUTOENCODER_UNET_PLAN.md`
- `docs/display_utility_fix.md`

## Files to Keep

### Core Pipeline
- `main.py`
- `core_code/` (entire directory)
- `utils/` (entire directory)
- `models/` (entire directory)

### Configuration and Data
- `requirements.txt`
- `settings.json`
- `config/`
- `Las/` (data files)

### Essential Documentation
- `README.md`
- `REORGANIZATION_COMPLETE.md`
- `CLAUDE.md`
- `GEMINI.md`
- `docs/summary_improvement.md`

### Examples
- `example/` (entire directory)

## Cleanup Strategy

1. **Safe Removal**: Use git rm to maintain history
2. **Backup First**: Ensure current state is committed
3. **Systematic Approach**: Remove by category
4. **Validation**: Test core functionality after cleanup

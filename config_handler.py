"""
DEPRECATED: config_handler.py has moved to utils.config_handler

This shim file provides backward compatibility for existing imports.
Please update your imports to use: from utils.config_handler import ...

This shim will be removed in a future version.
"""

import warnings

warnings.warn(
    "config_handler.py has moved to utils.config_handler. "
    "Please update your imports. This shim will be removed in v2.0.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export everything from the new location
from utils.config_handler import *

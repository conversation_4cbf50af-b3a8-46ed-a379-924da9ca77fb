"""
DEPRECATED: ml_core.py has moved to core_code.ml_core

This shim file provides backward compatibility for existing imports.
Please update your imports to use: from core_code.ml_core import ...

This shim will be removed in a future version.
"""

import warnings

warnings.warn(
    "ml_core.py has moved to core_code.ml_core. "
    "Please update your imports. This shim will be removed in v2.0.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export everything from the new location
from core_code.ml_core import *

"""
DEPRECATED: reporting.py has moved to utils.reporting

This shim file provides backward compatibility for existing imports.
Please update your imports to use: from utils.reporting import ...

This shim will be removed in a future version.
"""

import warnings

warnings.warn(
    "reporting.py has moved to utils.reporting. "
    "Please update your imports. This shim will be removed in v2.0.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export everything from the new location
from utils.reporting import *

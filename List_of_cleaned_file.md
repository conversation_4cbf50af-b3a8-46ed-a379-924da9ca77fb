# Codebase Structure and File Categorization

## Overview
This document provides a clear breakdown of the repository's structure, categorizing files and directories into two main groups: **Core Pipeline** and **Supporting Components**. This helps in understanding the key components required for execution versus those used for development, documentation, and analysis.

**Last Updated**: 2025-08-19

---

## 🚀 Core Pipeline Files & Directories
These components are essential for the primary functionality of the ML pipeline.

```
├── main.py                     # Main entry point with GUI workflow
├── data_handler.py             # LAS file operations and preprocessing
├── ml_core.py                  # Model registry and training pipeline
├── config_handler.py           # User interfaces and configuration
├── reporting.py                # Visualization and performance reporting
├── enhanced_preprocessing.py   # Advanced preprocessing pipeline
├── mlr_utils.py                # Linear regression utilities
├── gradient_diagnostics.py     # Gradient analysis utilities
├── data_leakage_detector.py    # Data validation utilities
├── requirements.txt            # Python dependencies
├── models/                     # Directory for all model implementations
├── utils/                      # Directory for all utility modules
├── Las/                        # Directory for input LAS files
└── config/                     # Directory for runtime configuration files
```

---

## 📚 Supporting Files & Directories
These components support development, experimentation, and documentation but are not part of the direct execution flow.

### Documentation & Planning
Files that provide context, plans, and guides for the project.
```
├── README.md                               # Main project documentation
├── List_of_cleaned_file.md                 # This structure and cleanup document
├── CLAUDE.md                               # AI assistant context file (Legacy)
├── GEMINI.md                               # AI assistant context file
├── 1_Clean_up_autoencoder_unet_plan.md     # Historical planning document
├── 2_optimize_saits_and_brits.md           # Historical planning document
├── quick_training_speedup_guide.py         # A guide written as a Python script
└── docs/                                   # Directory for all project documentation
```

### Development, Debugging & Helper Scripts
Scripts used for troubleshooting, fixing specific issues, or aiding development.
```
├── debug_slow_training.py                  # Script for debugging performance issues
└── optimized_base_model_fix.py             # Script containing a fix for a base model
```

### Generated Output & Cache Directories
These are automatically generated during runtime and can typically be ignored.
```
├── __pycache__/                            # Python bytecode cache
├── catboost_info/                          # CatBoost training logs and outputs
└── plots/                                  # Default directory for generated charts
```

### Archived, Example & Ambiguous Code
Code that is not part of the active pipeline but is kept for reference or as examples.
```
├── archives/                               # Historical code and test files
├── example/                                # Example notebooks and scripts
└── core_code/                              # Potentially core/reusable code snippets
```

### Tooling & Environment Configuration
Files used by development tools and for environment setup.
```
├── .gitignore                              # Specifies intentionally untracked files for Git
├── settings.json                           # VS Code or project-specific settings
├── .claude/                                # Configuration for the Claude AI assistant
├── .codellm/                               # Configuration for CodeLLM
└── .kiro/                                  # Configuration for the Kiro AI assistant
```

import os, glob
import copy
import lasio
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

# Import enhanced preprocessing (optional)
try:
    from core_code.preprocessing.enhanced_preprocessing import (
        enhanced_preprocessing_pipeline,
        enhanced_normalize_data,
        enhanced_create_sequences,
        enhanced_introduce_missingness
    )
    ENHANCED_PREPROCESSING_AVAILABLE = True
except ImportError:
    ENHANCED_PREPROCESSING_AVAILABLE = False
    print("Enhanced preprocessing not available. Using standard preprocessing.")

def load_las_files_from_directory(input_source):
    """Load LAS files from a directory or list of file paths into a DataFrame."""
    # Handle both directory path (string) and list of file paths
    if isinstance(input_source, str):
        # Input is a directory path
        las_files = glob.glob(os.path.join(input_source, '*.las'))
        if not las_files:
            print("No LAS files found in directory.")
            return pd.DataFrame(), {}, [], []
    elif isinstance(input_source, list):
        # Input is a list of file paths
        las_files = input_source
        if not las_files:
            print("No LAS files provided.")
            return pd.DataFrame(), {}, [], []
    else:
        print("Invalid input source. Must be directory path or list of file paths.")
        return pd.DataFrame(), {}, [], []

    frames, las_objs, wells = [], {}, []
    for f in las_files:
        try:
            las = lasio.read(f)
            well = las.well.WELL.value or os.path.splitext(os.path.basename(f))[0]

            # Convert LAS to DataFrame - handle different lasio versions
            if hasattr(las, 'to_df') and callable(las.to_df):
                df = las.to_df().reset_index()
            elif hasattr(las, 'df') and callable(las.df):
                df = las.df().reset_index()
            else:
                # Fallback - try to access df as property
                df = las.df.reset_index()

            depth_col = next((c for c in ['DEPT','DEPTH','MD'] if c in df.columns), None)
            if depth_col is None:
                print(f"{f}: no depth column.")
                continue
            df.rename(columns={depth_col:'MD'}, inplace=True)
            df['WELL'] = well
            frames.append(df)
            las_objs[well] = las
            wells.append(well)
            print(f"Loaded {well}")
        except Exception as e:
            print(f"Error {f}: {e}")

    if not frames:
        return pd.DataFrame(), {}, [], []

    comb = pd.concat(frames, ignore_index=True)
    log_names = [c for c in comb.columns if c not in ['MD','WELL']]
    return comb, las_objs, sorted(set(wells)), sorted(log_names)

def clean_log_data(df):
    """Basic cleaning."""
    rules = {'GR':(0,300),'NPHI':(0,1),'RHOB':(1.5,3.0),'DT':(40,200)}
    clean = df.copy()
    for col,(mn,mx) in rules.items():
        if col in clean.columns:
            clean[col] = np.where((clean[col]>=mn)&(clean[col]<=mx),clean[col],np.nan)
    return clean

def write_results_to_las(res_df, target_log, las_objs, out_dir):
    os.makedirs(out_dir, exist_ok=True)
    imp, pred, err = (f"{target_log}_imputed", f"{target_log}_pred", f"{target_log}_error")
    for well, las in las_objs.items():
        wdf = res_df[res_df['WELL']==well]
        if wdf.empty: continue
        new_las = copy.deepcopy(las)
        for mn in [imp,pred,err]:
            if mn in new_las.curves: new_las.delete_curve(mn)
        unit = las.curves[target_log].unit if target_log in las.curves else ""
        for mn,desc in [(imp,f"ML Imputed {target_log}"),(pred,f"ML Pred {target_log}"),(err,f"ML Err {target_log}")]:
            data = wdf.set_index('MD')[mn].reindex(new_las.index).values
            new_las.append_curve(mn, data, unit=unit, descr=desc)
        out = os.path.join(out_dir, f"{well}_imputed.las")
        new_las.write(out, version=2.0)
        print(f"Wrote {out}")

def normalize_data(df, columns, use_enhanced=False, scalers=None):
    """Normalizes the specified columns in the dataframe with improved NaN handling."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced normalization with winsorization...")
        return enhanced_normalize_data(df, columns, scalers=scalers)

    # Standard normalization
    df_scaled = df.copy()

    if scalers is None:
        # Fit and apply new scalers
        new_scalers = {}
        print("Fitting new scalers...")
        for col in columns:
            if col not in df.columns:
                print(f"Warning: Column '{col}' not found in dataframe")
                continue

            valid_data = df[col].dropna()

            if len(valid_data) == 0:
                print(f"Warning: No valid data for column '{col}', skipping normalization")
                new_scalers[col] = None
                continue

            scaler = StandardScaler()
            scaler.fit(valid_data.values.reshape(-1, 1))
            df_scaled[col] = scaler.transform(df[[col]])
            new_scalers[col] = scaler
            print(f"Normalized '{col}': method=standard, valid_data={len(valid_data)}/{len(df)} ({len(valid_data)/len(df):.1%})")
        
        return df_scaled, new_scalers
    else:
        # Apply existing scalers
        print("Applying existing scalers...")
        for col in columns:
            if col in scalers and scalers[col] is not None:
                df_scaled[col] = scalers[col].transform(df[[col]])
                valid_data = df[col].dropna()
                print(f"Normalized '{col}': method=standard, valid_data={len(valid_data)}/{len(df)} ({len(valid_data)/len(df):.1%})")
            else:
                print(f"Warning: Scaler for column '{col}' not found or is None. Column not transformed.")
        
        return df_scaled, scalers

def create_sequences(df, well_col, feature_cols, sequence_len=64, step=1, use_enhanced=False, allow_nan_cols=None):
    """Creates sequences and metadata from well data for deep learning models."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced sequence creation with valid interval detection...")
        # Directly use the underlying enhanced preprocessor to get both sequences and metadata
        from core_code.preprocessing.enhanced_preprocessing import EnhancedLogPreprocessor
        preprocessor = EnhancedLogPreprocessor(sequence_len=sequence_len, sequence_stride=step)
        sequences, metadata = preprocessor.create_sequences_enhanced(df, well_col, feature_cols)
        
        # Ensure sequences is a numpy array and not a list of DataFrames or other types
        if not isinstance(sequences, np.ndarray):
            print(f"Converting sequences from {type(sequences)} to numpy array")
            sequences = np.array(sequences, dtype=np.float32)
        
        print(f"Enhanced sequences shape: {sequences.shape}, type: {type(sequences)}, dtype: {sequences.dtype}")
        return sequences, metadata

    all_sequences = []
    metadata = []
    
    # Ensure dataframe is sorted by well and depth for correct indexing
    # Use the original index from the main dataframe for reassembly
    df_sorted = df.sort_values(by=[well_col, 'MD']).reset_index(drop=False)

    # Check if we should use progress tracking for this function too
    try:
        from utils.well_progress import create_well_progress_tracker
        use_progress_bar = True
    except ImportError:
        use_progress_bar = False

    wells = df_sorted[well_col].unique()

    # Initialize progress tracker for basic sequence creation
    if use_progress_bar and len(wells) > 5:  # Only use progress bar for multiple wells
        progress_tracker = create_well_progress_tracker(
            wells=list(wells),
            description="Creating basic sequences",
            show_current_well=True,
            suppress_warnings=False  # Keep warnings visible for basic processing
        )
    else:
        progress_tracker = None

    for well in wells:
        if progress_tracker:
            progress_tracker.start_well(well)

        well_df = df_sorted[df_sorted[well_col] == well]

        # Detect if we're in prediction mode (target column is mostly NaN)
        # This happens when the target column is intentionally masked during prediction
        is_prediction_mode = False
        if len(feature_cols) > 1:  # Only check if we have multiple columns
            target_col_idx = len(feature_cols) - 1  # Assume target is the last column
            target_nan_ratio = well_df[feature_cols[target_col_idx]].isna().mean()
            is_prediction_mode = target_nan_ratio > 0.95  # >95% NaN indicates prediction mode

        # Identify continuous intervals of data (no NaNs in features)
        if is_prediction_mode and len(feature_cols) > 1:
            # In prediction mode, only check feature columns (exclude target)
            feature_only_cols = feature_cols[:-1]  # All except last (target)
            is_valid = well_df[feature_only_cols].notna().all(axis=1)
            # Log prediction mode detection (only if not using progress tracker)
            if not progress_tracker:
                print(f"Well '{well}': Prediction mode detected, checking {len(feature_only_cols)} feature columns only")
        else:
            # In training mode, check all columns
            is_valid = well_df[feature_cols].notna().all(axis=1)
        # Get indices of changes in validity
        valid_changes = np.diff(is_valid.astype(int))
        interval_edges = np.where(valid_changes != 0)[0] + 1

        # Build a list of valid intervals [start, end]
        intervals = []
        if is_valid.iloc[0]:
            intervals.append([0])
        
        for edge in interval_edges:
            if len(intervals[-1]) == 1:
                intervals[-1].append(edge)
            else:
                intervals.append([edge])

        if len(intervals) > 0 and len(intervals[-1]) == 1:
            intervals[-1].append(len(well_df))

        for start, end in intervals:
            interval_len = end - start
            if interval_len < sequence_len:
                continue

            # Extract data for this valid interval
            interval_data = well_df.iloc[start:end]
            data_values = interval_data[feature_cols].values
            
            num_sequences_in_interval = (len(data_values) - sequence_len) // step + 1
            for i in range(num_sequences_in_interval):
                seq_start = i * step
                seq_end = seq_start + sequence_len
                
                # Get the original dataframe indices for this sequence
                original_indices = interval_data['index'][seq_start:seq_end].tolist()

                all_sequences.append(data_values[seq_start:seq_end])
                metadata.append({
                    'well': well,
                    'original_indices': original_indices
                })

        # Count sequences for this well
        well_sequences = sum(1 for meta in metadata if meta['well'] == well)

        # Finish well processing
        if progress_tracker:
            progress_tracker.finish_well(
                well_name=well,
                intervals=len(intervals),
                sequences=well_sequences,
                success=True
            )

    # Close progress tracker
    if progress_tracker:
        progress_tracker.close()

    if not all_sequences:
        print("Warning: No valid sequences could be created. Check data quality and sequence length.")
        return np.array([]), []

    # Print final summary only if not using progress tracker (which prints its own summary)
    if not progress_tracker:
        print(f"Created {len(all_sequences)} sequences of length {sequence_len}.")

    return np.array(all_sequences), metadata

def introduce_missingness(sequences, missing_rate=0.2, random_seed=42, use_enhanced=False,
                         target_col_name=None, feature_names=None):
    """Introduces missing values into the sequences for imputation training with improved strategy."""
    if use_enhanced and ENHANCED_PREPROCESSING_AVAILABLE:
        print("Using enhanced missing value introduction with realistic patterns...")
        result = enhanced_introduce_missingness(sequences, missing_rate, random_seed)
        
        # Ensure result is a numpy array
        if not isinstance(result, np.ndarray):
            print(f"Converting missing sequences from {type(result)} to numpy array")
            result = np.array(result, dtype=np.float32)
        
        print(f"Enhanced missing sequences shape: {result.shape}, type: {type(result)}, dtype: {result.dtype}")
        return result

    # Standard missing value introduction (existing implementation)
    np.random.seed(random_seed)
    sequences_with_missing = sequences.copy()

    # Get original shape
    n_sequences, seq_len, n_features = sequences.shape

    # Strategy: introduce missing values in chunks rather than randomly
    # This is more realistic for well log data where missing sections are common
    total_elements = np.prod(sequences.shape)
    n_missing = int(total_elements * missing_rate)

    # Create missing patterns
    missing_indices = []

    # 70% random individual missing values
    n_random = int(n_missing * 0.7)
    random_indices = np.random.choice(total_elements, size=n_random, replace=False)
    missing_indices.extend(random_indices)

    # 30% missing chunks (more realistic for well logs)
    n_chunks = int(n_missing * 0.3)
    chunk_size = 3  # Average chunk size

    for _ in range(n_chunks // chunk_size):
        # Random sequence and position
        seq_idx = np.random.randint(0, n_sequences)
        start_pos = np.random.randint(0, max(1, seq_len - chunk_size))
        feature_idx = np.random.randint(0, n_features)

        # Add chunk indices
        for pos in range(start_pos, min(start_pos + chunk_size, seq_len)):
            flat_idx = seq_idx * (seq_len * n_features) + pos * n_features + feature_idx
            if flat_idx < total_elements:
                missing_indices.append(flat_idx)

    # Remove duplicates and limit to desired count
    missing_indices = list(set(missing_indices))[:n_missing]

    # Apply missing values
    flat_view = sequences_with_missing.flatten()
    flat_view[missing_indices] = np.nan
    sequences_with_missing = flat_view.reshape(sequences.shape)

    actual_missing_rate = len(missing_indices) / total_elements
    print(f"Introduced {actual_missing_rate:.1%} missing values ({len(missing_indices)} elements)")
    print(f"Missing pattern: {len(random_indices)} random + {len(missing_indices) - len(random_indices)} chunked")

    return sequences_with_missing


def introduce_realistic_missingness(sequences, missing_rate=0.2, random_seed=42,
                                   target_col_name=None, feature_names=None,
                                   pattern_type='mixed'):
    """
    Introduces realistic missing data patterns that mimic real-world well log scenarios.

    This function creates more realistic missing data patterns compared to random missingness,
    including geological formations, tool failures, and operational issues.

    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        missing_rate: Overall proportion of missing values (default: 0.2)
        random_seed: Random seed for reproducibility
        target_col_name: Name of target column (for targeted missingness)
        feature_names: List of feature names
        pattern_type: Type of missing pattern ('mixed', 'geological', 'operational')

    Returns:
        sequences_with_missing: Sequences with realistic missing patterns
    """
    np.random.seed(random_seed)
    sequences_with_missing = sequences.copy()
    n_sequences, seq_len, n_features = sequences.shape

    print(f"🔧 Introducing realistic missing patterns (type: {pattern_type})...")

    total_elements = np.prod(sequences.shape)
    n_missing_target = int(total_elements * missing_rate)
    missing_count = 0

    if pattern_type in ['mixed', 'geological']:
        # 1. Geological Formation Boundaries (40% of missing data)
        # Simulate missing data at formation boundaries where tools may fail
        formation_missing = int(n_missing_target * 0.4)

        for seq_idx in range(n_sequences):
            if missing_count >= formation_missing:
                break

            # Create 2-3 formation boundaries per sequence
            n_boundaries = np.random.randint(2, 4)
            boundary_positions = np.random.choice(seq_len, size=n_boundaries, replace=False)

            for boundary_pos in boundary_positions:
                if missing_count >= formation_missing:
                    break

                # Missing section around boundary (3-8 points)
                gap_size = np.random.randint(3, 9)
                start_pos = max(0, boundary_pos - gap_size // 2)
                end_pos = min(seq_len, start_pos + gap_size)

                # Affect 1-2 features (tools that commonly fail together)
                n_affected_features = np.random.randint(1, 3)
                affected_features = np.random.choice(n_features, size=n_affected_features, replace=False)

                for pos in range(start_pos, end_pos):
                    for feat_idx in affected_features:
                        if missing_count < formation_missing:
                            sequences_with_missing[seq_idx, pos, feat_idx] = np.nan
                            missing_count += 1

    if pattern_type in ['mixed', 'operational']:
        # 2. Tool Failure Patterns (30% of missing data)
        # Simulate consecutive missing sections due to tool failures
        tool_failure_missing = int(n_missing_target * 0.3)

        for seq_idx in range(n_sequences):
            if missing_count >= n_missing_target:
                break

            # 1-2 tool failures per sequence
            n_failures = np.random.randint(1, 3)

            for _ in range(n_failures):
                if missing_count >= n_missing_target:
                    break

                # Tool failure duration (5-15 consecutive points)
                failure_duration = np.random.randint(5, 16)
                start_pos = np.random.randint(0, max(1, seq_len - failure_duration))
                end_pos = min(seq_len, start_pos + failure_duration)

                # Usually affects specific tool combinations
                if np.random.random() < 0.6:  # 60% chance of single tool failure
                    affected_features = [np.random.randint(0, n_features)]
                else:  # 40% chance of multiple tool failure
                    n_affected = np.random.randint(2, min(4, n_features + 1))
                    affected_features = np.random.choice(n_features, size=n_affected, replace=False)

                for pos in range(start_pos, end_pos):
                    for feat_idx in affected_features:
                        if missing_count < n_missing_target:
                            sequences_with_missing[seq_idx, pos, feat_idx] = np.nan
                            missing_count += 1

    # 3. Random Isolated Missing Values (remaining missing data)
    # Simulate random measurement errors or data transmission issues
    remaining_missing = n_missing_target - missing_count

    if remaining_missing > 0:
        # Create random missing values
        for _ in range(remaining_missing):
            seq_idx = np.random.randint(0, n_sequences)
            pos_idx = np.random.randint(0, seq_len)
            feat_idx = np.random.randint(0, n_features)

            # Only add if not already missing
            if not np.isnan(sequences_with_missing[seq_idx, pos_idx, feat_idx]):
                sequences_with_missing[seq_idx, pos_idx, feat_idx] = np.nan
                missing_count += 1

    actual_missing_rate = missing_count / total_elements
    print(f"✅ Realistic missing patterns introduced:")
    print(f"   • Total missing rate: {actual_missing_rate:.1%} ({missing_count} elements)")
    print(f"   • Pattern type: {pattern_type}")
    print(f"   • Geological boundaries: ~40% of missing data")
    print(f"   • Tool failures: ~30% of missing data")
    print(f"   • Random isolated: ~30% of missing data")

    return sequences_with_missing


def generate_geological_missing_patterns(sequences, formation_boundaries=None,
                                        missing_rate=0.15, random_seed=42):
    """
    Advanced function to generate geological-specific missing patterns.

    This function simulates missing data patterns that are specific to geological
    formations and drilling operations, providing even more realistic training scenarios.

    Args:
        sequences: Input sequences (n_sequences, seq_len, n_features)
        formation_boundaries: Optional list of known formation boundary positions
        missing_rate: Overall missing rate (default: 0.15)
        random_seed: Random seed for reproducibility

    Returns:
        sequences_with_missing: Sequences with geological missing patterns
    """
    np.random.seed(random_seed)
    sequences_with_missing = sequences.copy()
    n_sequences, seq_len, n_features = sequences.shape

    print(f"🌍 Generating advanced geological missing patterns...")

    total_elements = np.prod(sequences.shape)
    n_missing_target = int(total_elements * missing_rate)
    missing_count = 0

    for seq_idx in range(n_sequences):
        if missing_count >= n_missing_target:
            break

        # If formation boundaries are provided, use them; otherwise, estimate
        if formation_boundaries is None:
            # Estimate formation boundaries based on data characteristics
            # This is a simplified approach - in practice, you'd use geological knowledge
            estimated_boundaries = np.random.choice(seq_len, size=np.random.randint(3, 6), replace=False)
            estimated_boundaries = np.sort(estimated_boundaries)
        else:
            estimated_boundaries = formation_boundaries

        # Create missing patterns around each formation boundary
        for boundary in estimated_boundaries:
            if missing_count >= n_missing_target:
                break

            # Formation transition zone (where tools commonly fail)
            transition_size = np.random.randint(2, 8)
            start_pos = max(0, boundary - transition_size // 2)
            end_pos = min(seq_len, boundary + transition_size // 2)

            # Different tools fail in different geological conditions
            # Resistivity tools often fail in conductive formations
            # Density tools may fail in washed-out holes
            # Neutron tools affected by clay content

            failure_probability = {
                0: 0.3,  # First feature (e.g., GR) - generally reliable
                1: 0.5,  # Second feature (e.g., resistivity) - moderate failure
                2: 0.4,  # Third feature (e.g., density) - moderate failure
                3: 0.6   # Fourth feature (e.g., neutron) - higher failure rate
            }

            for pos in range(start_pos, end_pos):
                for feat_idx in range(n_features):
                    if missing_count >= n_missing_target:
                        break

                    # Apply feature-specific failure probability
                    prob = failure_probability.get(feat_idx, 0.4)
                    if np.random.random() < prob:
                        sequences_with_missing[seq_idx, pos, feat_idx] = np.nan
                        missing_count += 1

    actual_missing_rate = missing_count / total_elements
    print(f"✅ Geological missing patterns generated:")
    print(f"   • Missing rate: {actual_missing_rate:.1%} ({missing_count} elements)")
    print(f"   • Formation-aware patterns applied")
    print(f"   • Tool-specific failure probabilities used")

    return sequences_with_missing

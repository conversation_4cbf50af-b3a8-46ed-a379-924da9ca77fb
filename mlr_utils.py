"""
DEPRECATED: mlr_utils.py has moved to utils.mlr_utils

This shim file provides backward compatibility for existing imports.
Please update your imports to use: from utils.mlr_utils import ...

This shim will be removed in a future version.
"""

import warnings

warnings.warn(
    "mlr_utils.py has moved to utils.mlr_utils. "
    "Please update your imports. This shim will be removed in v2.0.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export everything from the new location
from utils.mlr_utils import *

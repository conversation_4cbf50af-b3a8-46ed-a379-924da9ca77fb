# Plan: Remove Autoencoder and U-Net Implementations from Main ML Pipeline

This document provides an actionable, step-by-step plan to remove failed/legacy Autoencoder and U-Net implementations from the main pipeline while keeping the rest of the system (SAITS, BRITS, Transformer, MLR, tree models, etc.) functional and clean.

Last updated: 2025-08-12
Target branch: feature/remove-autoencoder-unet

---

## 1) Impact Analysis

Identify all files and code paths that reference or implement Autoencoder or U-Net models.

- Model implementation files
  - models/simple_autoencoder.py
  - models/autoencoder.py
  - models/autoencoder-LAPTOP-3BQL777A.py
  - models/unet.py
  - models/advanced_models/enhanced_unet.py (if present)

- Core pipeline imports, registry, and references
  - ml_core.py
    - Imports: `from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet`
    - MODEL_REGISTRY entries: keys `autoencoder`, `unet`
    - Advanced model gating: `EnhancedUNet` conditional
    - Recommendation lists include `autoencoder`, `unet`, and optionally `enhanced_unet`
    - Stability/type mapping mentions `autoencoder`/`unet`
    - Training special-case: model_name in ["U-Net", "Autoencoder"]
  - ml_core_optimized.py (same style imports)

- Main workflow
  - main.py relies on MODEL_REGISTRY to build selection lists; removing registry entries hides the options automatically

- Config/menus
  - No standalone config files expose these models directly; menus derive from MODEL_REGISTRY. If external configs exist in your environment, search and update keys: `autoencoder`, `unet`.

- Tests/debug scripts and examples
  - archives/debug/* (e.g., debug_autoencoder_evaluation.py)
  - Any tests/scripts named like `*autoencoder*.py`, `*unet*.py`

- Documentation mentioning these models
  - README.md, CLAUDE.md, QWEN.md, codebase_structure.md (sections listing Autoencoder/U-Net)

Search commands (reference):
```
# Run in repo root
git grep -nEi "autoencoder|\bunet\b|enhanced_unet" -- ':!*.ipynb'
```

---

## 2) Dependency Mapping

- Registry-driven exposure: Removing registry entries (`autoencoder`, `unet`) disables selection in main menus and guards downstream code paths.
- Import-time side effects: Remove imports of SimpleAutoencoder/SimpleUNet to avoid ImportError when files are moved.
- Recommendation helpers: Functions that propose model names must stop referencing these models (`autoencoder`, `unet`, `enhanced_unet`).
- Training special-cases: Branches specialized for Autoencoder/U-Net should be removed or generalized.
- Advanced UNet: If `EnhancedUNet` is available, it is added to registry and to recommendations. Remove those references too to fully remove UNet family.
- Docs: Update to avoid promising unsupported models.

Safe-change principle: As long as MODEL_REGISTRY and imports are cleaned, the rest of the pipeline (data handling, evaluation, plots) remains unaffected.

---

## 3) File Organization Plan (move → oldfiles/)

- Create archival directories:
```
oldfiles/
  models/
    advanced/
  docs/
  tests/
  scripts/
```

- Move (preserving git history via `git mv`):
```
# Models
git mv models/simple_autoencoder.py oldfiles/models/
git mv models/autoencoder.py oldfiles/models/
git mv models/autoencoder-LAPTOP-*.py oldfiles/models/ || true
git mv models/unet.py oldfiles/models/
# Advanced UNet, if present
[ -f models/advanced_models/enhanced_unet.py ] && git mv models/advanced_models/enhanced_unet.py oldfiles/models/advanced/

# Debug/tests (examples; adapt to actual files)
[ -f debug_autoencoder_evaluation.py ] && git mv debug_autoencoder_evaluation.py oldfiles/scripts/
[ -d tests ] && git grep -lEi "autoencoder|\\bunet\\b" tests | xargs -I{} git mv {} oldfiles/tests/
```

- Keep code references out of main tree to prevent accidental re-imports.

- Alternative: If you prefer to delete, keep a branch tag (e.g., `pre-autoencoder-unet-removal`) for full rollback.

---

## 4) Code Cleanup Steps (precise changes)

Perform the following edits to detach Autoencoder and U-Net fully.

1) ml_core.py
- Remove basic model imports at top:
  - `from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet`
- Remove registry entries for `autoencoder` and `unet` (entire dict entries)
- Remove any reference to `EnhancedUNet` (advanced UNet) including:
  - Import symbol `EnhancedUNet`
  - Conditional block that adds `enhanced_unet` to ADVANCED_MODEL_CONFIGS
  - Any recommendation list that includes `enhanced_unet`
- Remove recommendations for `autoencoder` and `unet` in:
  - recommend_models_for_task
  - get_all_model_recommendations (if present)
- Remove stability/type mapping references:
  - Mapping of `autoencoder`/`unet` to a special mode
- Remove training special-case:
  - Branch using `if model_name in ['U-Net', 'Autoencoder']:`

2) ml_core_optimized.py
- Remove `from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet` and related flags.

3) Documentation
- Update README.md, CLAUDE.md, QWEN.md, codebase_structure.md to remove Autoencoder/U-Net from model lists.

4) Optional dead-code pruning
- If any utils expect these models explicitly, guard or remove those branches.

Example snippets to remove (for orientation):
```
# ml_core.py: imports
from models.simple_autoencoder import SimpleAutoencoder, SimpleUNet

# ml_core.py: registry keys
'autoencoder': { ... },
'unet': { ... },

# ml_core.py: advanced unet
if ADVANCED_MODELS_STATUS.get('enhanced_unet', False) and EnhancedUNet is not None:
    ADVANCED_MODEL_CONFIGS['enhanced_unet'] = { ... }

# ml_core.py: recommendations
recommendations.extend(['autoencoder', 'unet'])
...
recommendations.extend(['brits', 'enhanced_unet'])

# ml_core.py: stability/type mapping
elif 'autoencoder' in stability_model_type or 'unet' in stability_model_type:
    stability_model_type = 'autoencoder'

# ml_core.py: training special-case
if model_name in ['U-Net', 'Autoencoder']:
    model.fit(train_tensor, truth_tensor, ...)
```

Commit in small, testable changes:
```
# 1) Remove imports and registry entries
git add -p ml_core.py
# 2) Remove advanced unet additions and recommendations
git add -p ml_core.py
# 3) Clean ml_core_optimized.py imports
git add -p ml_core_optimized.py
# 4) Docs cleanup
git add -p README.md CLAUDE.md QWEN.md codebase_structure.md
```

---

## 5) Validation Plan

After moving files and cleaning code, verify functionality end-to-end.

1) Static checks
- Search for leftover references:
```
git grep -nEi "autoencoder|\bunet\b|enhanced_unet" -- ':!oldfiles/*' ':!*.ipynb'
```
Should return 0 results (outside oldfiles/).

2) Import sanity
```
python - <<'PY'
import importlib
print('Import ml_core...')
import ml_core
ml_core.print_model_registry_status()
print('OK')
PY
```
- Confirm `autoencoder`, `unet`, `enhanced_unet` are NOT listed in available models or recommendations.

3) Quick functional smoke test
- Run a minimal pipeline with supported models (choose any 1–2): SAITS/BRITS or a shallow model like XGBoost.
- Ensure training and evaluation complete; ensure LAS export still works via write_results_to_las.

4) UI menu check (main.py)
- Start the app; confirm model selection menu no longer shows Autoencoder/U-Net.

5) Documentation review
- Skim README.md and internal docs to ensure no stale claims.

---

## 6) Rollback Strategy

Choose one of the following:

- Git-based rollback (preferred)
  - Use a feature branch: `git checkout -b feature/remove-autoencoder-unet`
  - Squash merges for easy revert
  - If needed: `git revert <merge_commit_sha>` (or `git reset --hard <pre-merge-sha>` during development)

- File-based rollback
  - Move files back:
```
git mv oldfiles/models/simple_autoencoder.py models/
git mv oldfiles/models/autoencoder.py models/
git mv oldfiles/models/unet.py models/
[ -f oldfiles/models/advanced/enhanced_unet.py ] && git mv oldfiles/models/advanced/enhanced_unet.py models/advanced_models/
```
  - Reintroduce registry entries and imports (use previous commits as source)

- Branch/tag preservation
  - Create a tag before removal: `git tag pre-autoencoder-unet-removal`
  - Checkout anytime: `git checkout pre-autoencoder-unet-removal`

---

## 7) Suggested PR Checklist

- [ ] Old files moved to oldfiles/ with `git mv`
- [ ] Imports of SimpleAutoencoder/SimpleUNet removed
- [ ] MODEL_REGISTRY cleaned (no `autoencoder`, `unet`)
- [ ] Advanced `enhanced_unet` references removed
- [ ] Recommendations updated
- [ ] Special-case training path removed or generalized
- [ ] Docs updated (README.md, CLAUDE.md, QWEN.md, codebase_structure.md)
- [ ] `git grep` shows no references
- [ ] Smoke test passes with SAITS/BRITS/MLR/XGBoost

---

## 8) Notes

- If any downstream analysis notebooks/examples rely on Autoencoder/U-Net, relocate them under `oldfiles/` or update to SAITS/BRITS.
- Keep commits modular to facilitate partial rollback if needed.


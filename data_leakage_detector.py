"""
DEPRECATED: data_leakage_detector.py has moved to core_code.preprocessing.data_leakage_detector

This shim file provides backward compatibility for existing imports.
Please update your imports to use: from core_code.preprocessing.data_leakage_detector import ...

This shim will be removed in a future version.
"""

import warnings

warnings.warn(
    "data_leakage_detector.py has moved to core_code.preprocessing.data_leakage_detector. "
    "Please update your imports. This shim will be removed in v2.0.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export everything from the new location
from core_code.preprocessing.data_leakage_detector import *

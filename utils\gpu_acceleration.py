"""
GPU Acceleration Utilities for ML Log Prediction
Specialized GPU optimization utilities for deep learning models
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
import warnings
import time
import platform
import os

class GPUManager:
    """Advanced GPU management and optimization utilities."""
    
    def __init__(self, device_id: Optional[int] = None):
        """
        Initialize GPU manager.
        
        Args:
            device_id: Specific GPU device ID (None for auto-selection)
        """
        self.device_id = device_id
        self.device = self._select_best_device()
        self.mixed_precision_enabled = False
        self.memory_pool = None
        
        if self.device.type == 'cuda':
            self._initialize_gpu()
    
    def _select_best_device(self) -> torch.device:
        """Select the best available GPU device."""
        if not torch.cuda.is_available():
            print("💻 CUDA not available, using CPU")
            return torch.device('cpu')
        
        if self.device_id is not None:
            if self.device_id >= torch.cuda.device_count():
                print(f"⚠️ GPU {self.device_id} not available, using GPU 0")
                self.device_id = 0
            device = torch.device(f'cuda:{self.device_id}')
        else:
            # Auto-select best GPU based on memory
            best_gpu = 0
            max_memory = 0
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                if props.total_memory > max_memory:
                    max_memory = props.total_memory
                    best_gpu = i
            
            device = torch.device(f'cuda:{best_gpu}')
            self.device_id = best_gpu
        
        props = torch.cuda.get_device_properties(device)
        print(f"🚀 Selected GPU {self.device_id}: {props.name}")
        print(f"   Memory: {props.total_memory / 1e9:.1f} GB")
        print(f"   Compute Capability: {props.major}.{props.minor}")
        
        return device
    
    def _initialize_gpu(self) -> None:
        """Initialize GPU settings and optimizations."""
        torch.cuda.set_device(self.device)
        
        # Clear cache
        torch.cuda.empty_cache()
        
        # Enable cuDNN benchmarking for consistent input sizes
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # Check for mixed precision support
        if hasattr(torch.cuda, 'amp') and torch.cuda.get_device_capability()[0] >= 7:
            self.mixed_precision_enabled = True
            print("⚡ Mixed precision training available")
        
        print("✅ GPU initialized successfully")
    
    def _apply_triton_fix(self) -> bool:
        """Apply comprehensive Windows compatibility fixes."""
        try:
            if platform.system() == 'Windows':
                # Set environment variable to suppress Triton errors
                os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True,max_split_size_mb:512'
                
                # Set dynamo configuration to suppress errors
                if hasattr(torch, '_dynamo'):
                    torch._dynamo.config.suppress_errors = True
                
                # Disable torch.compile for problematic backends
                if hasattr(torch, '_inductor'):
                    torch._inductor.config.disable_progress = True
                
                # Check for MSVC/Windows SDK
                self._check_windows_build_tools()
                
                # CUDA compatibility check
                self._check_cuda_compatibility()
                
                # Disable Triton if components are missing
                if not self._has_required_windows_tools():
                    os.environ['DISABLE_TRITON'] = '1'
                    print("⚠️ Disabling Triton due to missing Windows build tools")
            return True
        except Exception:
            return False
    
    def _check_windows_build_tools(self) -> None:
        """Check for required Windows build tools."""
        try:
            import subprocess
            from pathlib import Path
            
            # Check for MSVC
            msvc_paths = [
                r"C:\Program Files (x86)\Microsoft Visual Studio",
                r"C:\Program Files\Microsoft Visual Studio",
                r"C:\BuildTools"
            ]
            
            msvc_found = any(Path(path).exists() for path in msvc_paths)
            
            # Check for Windows SDK
            sdk_paths = [
                r"C:\Program Files (x86)\Windows Kits\10",
                r"C:\Program Files\Windows Kits\10"
            ]
            
            sdk_found = any(Path(path).exists() for path in sdk_paths)
            
            if not msvc_found:
                print("⚠️ WARNING: MSVC not found. Install Visual Studio Build Tools")
            if not sdk_found:
                print("⚠️ WARNING: Windows SDK not found. Install Windows SDK")
                
        except Exception:
            pass
    
    def _check_cuda_compatibility(self) -> None:
        """Check CUDA compatibility and apply fixes."""
        try:
            if torch.cuda.is_available():
                cuda_version = torch.version.cuda
                if cuda_version:
                    print(f"✅ CUDA {cuda_version} detected")
                    
                    # Check for cuDNN
                    if torch.backends.cudnn.is_available():
                        print(f"✅ cuDNN {torch.backends.cudnn.version()} available")
                    else:
                        print("⚠️ WARNING: cuDNN not available")
                else:
                    print("⚠️ WARNING: CUDA version not detected")
            else:
                print("⚠️ WARNING: CUDA not available, falling back to CPU")
                
        except Exception as e:
            print(f"⚠️ CUDA compatibility check failed: {e}")
    
    def _has_required_windows_tools(self) -> bool:
        """Check if required Windows build tools are available."""
        try:
            import subprocess
            
            # Check for cl.exe (MSVC compiler)
            cl_result = subprocess.run(['where', 'cl'], 
                                       capture_output=True, text=True, shell=True)
            has_cl = cl_result.returncode == 0
            
            # Check for Windows SDK
            sdk_check = subprocess.run(['where', 'rc'], 
                                     capture_output=True, text=True, shell=True)
            has_rc = sdk_check.returncode == 0
            
            return has_cl and has_rc
            
        except Exception:
            return False
    
    def enable_mixed_precision(self) -> bool:
        """
        Enable mixed precision training if supported.
        
        Returns:
            True if mixed precision is enabled, False otherwise
        """
        if self.device.type != 'cuda':
            print("⚠️ Mixed precision requires CUDA")
            return False
        
        if not hasattr(torch.cuda, 'amp'):
            print("⚠️ Mixed precision not supported in this PyTorch version")
            return False
        
        capability = torch.cuda.get_device_capability()
        if capability[0] < 7:
            print(f"⚠️ Mixed precision requires compute capability >= 7.0 (current: {capability[0]}.{capability[1]})")
            return False
        
        self.mixed_precision_enabled = True
        print("⚡ Mixed precision training enabled")
        return True
    
    def optimize_model(self, model: nn.Module,
                      input_shape: Tuple[int, ...] = None,
                      compile_model: bool = False) -> nn.Module:
        """
        Optimize model for GPU execution.
        
        Args:
            model: PyTorch model to optimize
            input_shape: Expected input shape for optimization
            compile_model: Whether to compile the model (PyTorch 2.0+)
            
        Returns:
            Optimized model
        """
        # Move model to GPU
        model = model.to(self.device)
        
        # Set to evaluation mode for optimization
        was_training = model.training
        model.eval()
        
        # Warm up GPU with dummy forward pass
        if input_shape is not None:
            self._warmup_gpu(model, input_shape)
        
        # Compile model if supported (PyTorch 2.0+) with Triton fallback
        if compile_model and hasattr(torch, 'compile'):
            try:
                # Apply Triton fix for Windows compatibility
                self._apply_triton_fix()
                
                model = torch.compile(model)
                print("🔥 Model compiled with torch.compile")
            except RuntimeError as e:
                if "triton" in str(e).lower():
                    print(f"⚠️ Triton compilation error (expected on Windows): {e}")
                    print("   Falling back to uncompiled model")
                else:
                    print(f"⚠️ Model compilation failed: {e}")
            except Exception as e:
                print(f"⚠️ Model compilation failed: {e}")
        
        # Restore training mode
        if was_training:
            model.train()
        
        return model
    
    def _warmup_gpu(self, model: nn.Module, input_shape: Tuple[int, ...]) -> None:
        """Warm up GPU with dummy forward passes."""
        print("🔥 Warming up GPU...")
        
        # Create dummy input
        dummy_input = torch.randn(input_shape, device=self.device)
        
        # Perform several warm-up iterations
        with torch.no_grad():
            for _ in range(5):
                try:
                    _ = model(dummy_input)
                except Exception as e:
                    print(f"⚠️ GPU warm-up failed: {e}")
                    break
        
        torch.cuda.synchronize()
        print("✅ GPU warm-up completed")
    
    def create_mixed_precision_trainer(self) -> Tuple[Optional[Any], Optional[Any]]:
        """
        Create mixed precision training components.
        
        Returns:
            Tuple of (GradScaler, autocast context manager)
        """
        if not self.mixed_precision_enabled:
            return None, None
        
        try:
            from torch.cuda.amp import GradScaler, autocast
            scaler = GradScaler()
            return scaler, autocast
        except ImportError:
            print("⚠️ Mixed precision components not available")
            return None, None
    
    def optimize_memory_usage(self, aggressive: bool = False) -> Dict[str, float]:
        """
        Optimize GPU memory usage.
        
        Args:
            aggressive: Whether to use aggressive memory optimization
            
        Returns:
            Memory statistics before and after optimization
        """
        if self.device.type != 'cuda':
            return {'message': 'GPU not available'}
        
        # Get initial memory stats
        initial_stats = self.get_memory_stats()
        
        # Clear cache
        torch.cuda.empty_cache()
        
        if aggressive:
            # Force garbage collection
            import gc
            gc.collect()
            
            # Set memory fraction to be more conservative
            torch.cuda.set_per_process_memory_fraction(0.7, device=self.device)
        
        # Get final memory stats
        final_stats = self.get_memory_stats()
        
        print(f"🧹 Memory optimization completed")
        print(f"   Before: {initial_stats['allocated_gb']:.2f} GB allocated")
        print(f"   After: {final_stats['allocated_gb']:.2f} GB allocated")
        print(f"   Freed: {initial_stats['allocated_gb'] - final_stats['allocated_gb']:.2f} GB")
        
        return {
            'initial': initial_stats,
            'final': final_stats,
            'freed_gb': initial_stats['allocated_gb'] - final_stats['allocated_gb']
        }
    
    def get_memory_stats(self) -> Dict[str, float]:
        """Get detailed GPU memory statistics."""
        if self.device.type != 'cuda':
            return {'message': 'GPU not available'}
        
        allocated = torch.cuda.memory_allocated(self.device) / 1e9
        reserved = torch.cuda.memory_reserved(self.device) / 1e9
        max_allocated = torch.cuda.max_memory_allocated(self.device) / 1e9
        max_reserved = torch.cuda.max_memory_reserved(self.device) / 1e9
        
        props = torch.cuda.get_device_properties(self.device)
        total = props.total_memory / 1e9
        
        return {
            'allocated_gb': allocated,
            'reserved_gb': reserved,
            'max_allocated_gb': max_allocated,
            'max_reserved_gb': max_reserved,
            'total_gb': total,
            'free_gb': total - reserved,
            'utilization_percent': (allocated / total) * 100
        }
    
    def benchmark_model(self, model: nn.Module, input_shape: Tuple[int, ...],
                       num_iterations: int = 100) -> Dict[str, float]:
        """
        Benchmark model performance on GPU.
        
        Args:
            model: Model to benchmark
            input_shape: Input shape for benchmarking
            num_iterations: Number of iterations for benchmarking
            
        Returns:
            Benchmark results
        """
        model = model.to(self.device)
        model.eval()
        
        # Create dummy input
        dummy_input = torch.randn(input_shape, device=self.device)
        
        # Warm up
        with torch.no_grad():
            for _ in range(10):
                _ = model(dummy_input)
        
        torch.cuda.synchronize()
        
        # Benchmark forward pass
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model(dummy_input)
        
        torch.cuda.synchronize()
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time_ms = (total_time / num_iterations) * 1000
        throughput = num_iterations / total_time
        
        # Memory usage during inference
        memory_stats = self.get_memory_stats()
        
        results = {
            'avg_inference_time_ms': avg_time_ms,
            'throughput_samples_per_sec': throughput,
            'total_time_sec': total_time,
            'num_iterations': num_iterations,
            'memory_usage_gb': memory_stats['allocated_gb'],
            'device': str(self.device)
        }
        
        print(f"📊 Benchmark Results:")
        print(f"   Average inference time: {avg_time_ms:.2f} ms")
        print(f"   Throughput: {throughput:.1f} samples/sec")
        print(f"   Memory usage: {memory_stats['allocated_gb']:.2f} GB")
        
        return results
    
    def profile_model(self, model: nn.Module, input_shape: Tuple[int, ...],
                     num_iterations: int = 10) -> Dict[str, Any]:
        """
        Profile model execution with detailed timing.
        
        Args:
            model: Model to profile
            input_shape: Input shape for profiling
            num_iterations: Number of iterations for profiling
            
        Returns:
            Profiling results
        """
        if self.device.type != 'cuda':
            print("⚠️ GPU profiling requires CUDA")
            return {}
        
        model = model.to(self.device)
        model.eval()
        
        dummy_input = torch.randn(input_shape, device=self.device)
        
        # Use PyTorch profiler
        with torch.profiler.profile(
            activities=[torch.profiler.ProfilerActivity.CPU, torch.profiler.ProfilerActivity.CUDA],
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:
            with torch.no_grad():
                for _ in range(num_iterations):
                    _ = model(dummy_input)
        
        # Get profiling results
        key_averages = prof.key_averages().table(sort_by="cuda_time_total", row_limit=10)
        
        results = {
            'profiler_output': key_averages,
            'total_cuda_time': sum([item.cuda_time_total for item in prof.key_averages()]),
            'total_cpu_time': sum([item.cpu_time_total for item in prof.key_averages()]),
            'num_iterations': num_iterations
        }
        
        print("🔍 Model Profiling Results:")
        print(key_averages)
        
        return results
    
    def cleanup(self) -> None:
        """Clean up GPU resources."""
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
            print("🧹 GPU resources cleaned up")

class DataParallelManager:
    """Manager for data parallel training across multiple GPUs."""
    
    def __init__(self):
        """Initialize data parallel manager."""
        self.num_gpus = torch.cuda.device_count()
        self.available_gpus = list(range(self.num_gpus))
        
        if self.num_gpus > 1:
            print(f"🔥 Multiple GPUs detected: {self.num_gpus}")
            for i in range(self.num_gpus):
                props = torch.cuda.get_device_properties(i)
                print(f"   GPU {i}: {props.name} ({props.total_memory / 1e9:.1f} GB)")
        else:
            print("💻 Single GPU or CPU mode")
    
    def setup_data_parallel(self, model: nn.Module, 
                           gpu_ids: Optional[List[int]] = None) -> nn.Module:
        """
        Setup model for data parallel training.
        
        Args:
            model: Model to parallelize
            gpu_ids: List of GPU IDs to use (None for all available)
            
        Returns:
            Data parallel model
        """
        if self.num_gpus <= 1:
            print("⚠️ Data parallel requires multiple GPUs")
            return model
        
        if gpu_ids is None:
            gpu_ids = self.available_gpus
        
        # Filter available GPUs
        gpu_ids = [gpu_id for gpu_id in gpu_ids if gpu_id < self.num_gpus]
        
        if len(gpu_ids) <= 1:
            print("⚠️ Not enough GPUs for data parallel")
            return model
        
        # Setup data parallel
        model = nn.DataParallel(model, device_ids=gpu_ids)
        print(f"🔥 Data parallel setup complete: GPUs {gpu_ids}")
        
        return model
    
    def calculate_optimal_batch_size(self, base_batch_size: int, 
                                   num_gpus: Optional[int] = None) -> int:
        """
        Calculate optimal batch size for multi-GPU training.
        
        Args:
            base_batch_size: Base batch size for single GPU
            num_gpus: Number of GPUs to use
            
        Returns:
            Optimal batch size for multi-GPU
        """
        if num_gpus is None:
            num_gpus = self.num_gpus
        
        if num_gpus <= 1:
            return base_batch_size
        
        # Scale batch size with number of GPUs
        optimal_batch_size = base_batch_size * num_gpus
        
        print(f"📊 Optimal batch size for {num_gpus} GPUs: {optimal_batch_size}")
        return optimal_batch_size

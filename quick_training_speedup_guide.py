"""
Quick Training Speed-Up Guide (Non-Executable Reference)
=======================================================

This file is purely DOCUMENTATION to remind you which targeted edits to apply
inside `models/advanced_models/base_model.py` (or the current base training loop).

All code fragments below are wrapped inside this triple-quoted string so <PERSON><PERSON><PERSON>
won't attempt to resolve the symbolic names (self, torch, optimizer, etc.).

1. Disable gradient checkpointing by default (around original line ~230)
-----------------------------------------------------------------------
Original (or previous default):
    disable_checkpoint = self.model_params.get('disable_checkpoint', False)

Change to:
    disable_checkpoint = self.model_params.get('disable_checkpoint', True)  # default now True

2. Increase minimum effective batch size (around line ~193)
----------------------------------------------------------
Previous:
    batch_size = batch_size or self.batch_size

Change to (enforce at least 128 if possible):
    batch_size = batch_size or max(self.batch_size, 128)

3. DataLoader performance tweaks (around line ~213)
--------------------------------------------------
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,          # Parallel data loading
        pin_memory=True,        # Faster host->GPU copies
        persistent_workers=True # Avoid worker respawn overhead
    )

Adjust `num_workers` based on CPU cores (rule of thumb: cores/2 .. cores-1).

4. Non-blocking GPU transfers (around lines ~240-242)
----------------------------------------------------
    if torch.cuda.is_available():
        batch_data = batch_data.cuda(non_blocking=True)
        batch_truth = batch_truth.cuda(non_blocking=True)

Requires DataLoader `pin_memory=True` for best effect.

5. Mixed precision (AMP) integration (insert after data moved to device)
-----------------------------------------------------------------------
Add once before the epoch loop (after allocations, ~line 223):
    scaler = torch.cuda.amp.GradScaler()

Modify training step (around lines ~245-253):
    optimizer.zero_grad(set_to_none=True)
    with torch.cuda.amp.autocast():
        if disable_checkpoint:
            outputs = self.model(batch_data)
        else:
            outputs = checkpoint(checkpoint_fn, batch_data)
        loss = torch.nn.functional.mse_loss(outputs, batch_truth)

    scaler.scale(loss).backward()
    scaler.step(optimizer)
    scaler.update()

Ensure model + optimizer created BEFORE scaler instantiation (common practice).

6. Reduce validation frequency
------------------------------
If currently validating every epoch, switch to interval (e.g. every 5 epochs):
    if (epoch + 1) % 5 == 0:
        run_validation()

7. (Optional) Zero grad optimization
------------------------------------
Already shown above: use `optimizer.zero_grad(set_to_none=True)` for slight speed & lower memory.

8. (Optional) Gradient accumulation (if memory constrained but want larger effective batch)
------------------------------------------------------------------------------------------
    accumulation_steps = max(1, target_effective_batch // batch_size)
    optimizer.zero_grad(set_to_none=True)
    for step, (batch_data, batch_truth) in enumerate(train_loader):
        with torch.cuda.amp.autocast():
            outputs = self.model(batch_data)
            loss = criterion(outputs, batch_truth) / accumulation_steps
        scaler.scale(loss).backward()
        if (step + 1) % accumulation_steps == 0:
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad(set_to_none=True)

9. (Optional) cudnn.benchmark
------------------------------
At startup (if input shape mostly static):
    torch.backends.cudnn.benchmark = True

10. (Optional) Compile model (PyTorch 2.x)
-----------------------------------------
After model creation (before training loop):
    if hasattr(torch, 'compile'):
        model = torch.compile(model)

Keep this separate from AMP testing; measure speed gains (may vary).

Verification Checklist
----------------------
[] Larger batch size prints / logs
[] Non-blocking transfers enabled (check GPU utilization improvement)
[] AMP reduces iteration time & memory (watch for Inf/NaN with GradScaler enabled)
[] Validation now less frequent
[] No functional regression in loss curves

NOTE: Apply changes incrementally and profile after each for clarity.
"""
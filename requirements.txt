# Core data science libraries
pandas>=1.3.0
numpy>=1.21.0

# Machine learning libraries
scikit-learn>=1.0.0
xgboost>=1.5.0
lightgbm>=3.2.0
catboost>=1.0.0

# Deep learning libraries
torch>=1.9.0
pypots>=0.2.0
monai>=0.9.0
tqdm>=4.62.0
scipy>=1.7.0
statsmodels>=0.13.0 # Optional, for MLR diagnostics (VIF)

# Well log data handling
lasio>=0.30

# Visualization
matplotlib>=3.5.0
plotly>=5.0.0
seaborn>=0.11.0

# Hyperparameter optimization
optuna>=3.0.0

# Performance monitoring
memory-profiler>=0.60.0
psutil>=5.8.0

# Advanced transformers dependency removed (transformer model removed)

# Code quality and linting (referenced in CLAUDE.md and README.md)
flake8>=5.0.0

# Enhanced preprocessing (used in archives/second_stage/cp_preconditioning/preprocessing.py)
missingno>=0.5.0

# GUI for file dialogs (tkinter is usually included with Python)
# tkinter is part of standard library, no need to install

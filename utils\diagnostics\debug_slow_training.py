#!/usr/bin/env python3
"""
Debug script to identify slow training bottlenecks in SAITS/BRITS pipeline.
Run this before making changes to establish baseline performance.
"""

import time
import torch
import numpy as np

def profile_training_components():
    """Profile different components to identify bottlenecks."""
    print("🔍 PROFILING TRAINING COMPONENTS")
    print("=" * 50)
    
    # Check PyTorch setup
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # Check current settings
    print(f"TF32 enabled: {torch.backends.cuda.matmul.allow_tf32}")
    print(f"CuDNN benchmark: {torch.backends.cudnn.benchmark}")
    
    print("\n" + "=" * 50)
    print("RECOMMENDATIONS:")
    
    if not torch.backends.cuda.matmul.allow_tf32:
        print("❌ TF32 is DISABLED - this could slow training by 20-30%")
        print("   Fix: Add torch.backends.cuda.matmul.allow_tf32 = True")
    else:
        print("✅ TF32 is enabled")
    
    if not torch.backends.cudnn.benchmark:
        print("❌ CuDNN benchmark is DISABLED - this could slow training")
        print("   Fix: Add torch.backends.cudnn.benchmark = True")
    else:
        print("✅ CuDNN benchmark is enabled")
    
    print("\n" + "=" * 50)
    print("NEXT STEPS:")
    print("1. Run your training and time one epoch")
    print("2. Apply the quick fixes from quick_training_speedup_guide.py")
    print("3. Re-run training and compare epoch times")
    print("4. Expected speedup: 2-4x faster epochs")

def identify_model_type_from_main():
    """Help identify which model type is being used."""
    print("\n🔍 MODEL TYPE DETECTION")
    print("=" * 50)
    print("Check your main.py output for these lines:")
    print("- 'Advanced deep learning models module loaded' → Using PyPOTS models")
    print("- 'Training PyPOTS-based model: SAITS' → SAITS model")
    print("- 'Training PyPOTS-based model: BRITS' → BRITS model") 
    print("- 'Training with DataLoader interface' → Custom models")
    
    print("\nBased on the model type, apply these fixes:")
    print("📍 PyPOTS Models (SAITS/BRITS):")
    print("   - Increase batch_size in hparams")
    print("   - Enable TF32 and mixed precision")
    print("   - Reduce epochs or add early stopping")
    
    print("📍 Custom Models (base_model.py):")
    print("   - Set disable_checkpoint: True")
    print("   - Apply all fixes from quick_training_speedup_guide.py")

if __name__ == "__main__":
    profile_training_components()
    identify_model_type_from_main()